# Design Document: AIPane Chat V2 – Ground-Up Rebuild with `assistant-ui`

This document supersedes all previous drafts for Chat V2. It **does not touch the existing `app/api/aipane/chat` implementation (v1)** – we will build a **parallel, cleaner implementation** that can fully replace v1 once stabilised.

## Core Goals

1. **aipane-scoped** – Every new file lives under the `app/api/aipane` or `app/(conv)/aipane` subtree so we never collide with other chat experiments.
2. **`assistant-ui` first** – Use the official `useAssistant` / `streamUI` primitives; avoid writing a custom streaming layer.
3. **Keep proven business logic** – Reuse our existing authentication, rate-limiting, execution-step tracing, and logging utilities.
4. **Single source for contextIds** – Persist selected document ids **once** on the `AiConversation` (metadata column, following the `AIGeneration.metadata` convention so we always know _which_ question node kicked off the chat). **`AiMessage` keeps its own `metadata` column for run-completion stats, but tool calls continue to be stored in `AiExecutionStep`.**
5. **Developer-friendly for coding agents** – Explicit file paths, types, and TODOs are called out so an AI can implement without guess-work.

## Phases at a Glance

| Phase | Outcome                                                               |
| ----- | --------------------------------------------------------------------- |
| 0     | Project scaffolding + Prisma migration                                |
| 1     | New backend route `app/api/aipane/assistant/route.ts`                 |
| 2     | New frontend chat component `app/(conv)/aipane/components/ChatV2.tsx` |
| 3     | README & clean-up                                                     |

---

## Phase 0 — Scaffolding & Schema Evolution

### 0.2 Extend Prisma schema

Add the `metadata` column to **both** conversation and message models. Use the same JSON type/constraints we already rely on in `AIGeneration`.

```prisma
model AiConversation {
  // …existing fields
  metadata Json? @map("metadata") // { contextIds: string[] }
}

model AiMessage {
  // …existing fields
  metadata Json? @map("metadata") // { runCompleted: boolean, tokenUsage: … }
}
```

_Rationale:_ Conversation-level metadata answers “Using **X** context” exactly once at creation; per-message metadata captures stats of that particular turn – these are orthogonal concerns.

---

### 0.3 Apply migration

```bash
npx prisma migrate dev --name "add_metadata_to_ai_conversation"
```

The generated migration file will update the `ai_conversations` table.

---

## Phase 1 — Backend Architecture (`streamUI` + Existing Business Logic)

### 1.1 File location

`app/api/aipane/assistant/route.ts`

### 1.2 Request contract (`POST`)

Type resides in `app/api/aipane/assistant/types.ts`.

```ts
type AIPaneChatRequest = {
  messages: CoreMessage[] // `assistant-ui` CoreMessage
  conversationId?: string // thread_<cuid>
  metadata?: {
    // NOTE: no longer required on every turn – provided **only** on the very first request
    contextIds?: string[] // Optional on creation
  }
  model?: string // fallback to gpt-4o etc.
  settings?: Record<string, unknown> // temperature etc.
}
```

### 1.3 High-level flow

1. **Auth & Rate-limit** – `getServerSession` + `isRateLimited`.
2. **Body validation** – Ensure `messages` array + `conversationId` format.
3. **Context Management**
   - Fetch latest 15 messages (`AiMessage`) for `conversationId` **from the **conversation cache** if available (`libs/cache/conversation-cache`). Fallback to DB if missing and then hydrate the cache.**
   - Read `contextIds` from `AiConversation.metadata`; build the "Using X context" banner and the system prompt contents.
   - _Do **not** overwrite `metadata.contextIds` after creation._
4. **`streamUI` invocation** – Pass model, messages, and `tools` (search, code-exec, etc.).
5. **Tool handling**
   - When the SDK surfaces a tool call, hook into our existing `createExecutionStepCollector()` API.
   - Immediately stream a placeholder React component to the client via `ui.append(<ToolSpinner …/>)`.
   - Execute the tool and stream the rendered result `<ToolResult …/>`.
6. **Persistence (`onFinish`)**
   - Save the assistant response as an `AiMessage`.
   - Persist any execution steps via the collector.
   - If `metadata.contextIds` changed, merge + save to `AiConversation.metadata`.
   - Log usage via `createAIUsage(AIUsageType.CHAT)`.

> Implementation skeleton can be copied from the existing v1 route (`app/api/aipane/chat/route.ts`) but simplified thanks to `streamUI`.

### 1.4 Constants

Place config in `app/api/aipane/assistant/config.ts`:

```ts
export const RATE_LIMIT_MS = 5_000
export const DEFAULT_MODEL = 'gpt-4.1'
export const MAX_HISTORY_MESSAGES = 15
```

---

## Phase 2 — Frontend (`useAssistant`)

### 2.1 Component path

`app/(conv)/aipane/components/ChatV2.tsx`

### 2.2 Hook Usage

```tsx
const { messages, handleSubmit, status } = useAssistant({
  api: '/api/aipane/assistant',
})
```

### 2.3 Context Picker UI

The picker is shown **only while creating a new conversation**. After the first turn the backend already knows the contextIds, so subsequent calls omit metadata.

```ts
handleSubmit(e, {
  body: {}, // metadata only on first turn – subsequent calls are empty
})
```

The assets sidebar (existing component) continues to read from the conversation cache so previously selected documents remain visible.

---

## Phase 3 — README & Clean-Up

1. Add `app/api/aipane/assistant/README.md` describing the backend flow.
2. Deprecate v1 only when v2 reaches feature parity.
3. Remove any duplicated types or utilities.

---

## FAQ / Common Questions

- **Why not store contextIds on every message?**
  Because the selection is _conversation-level state_ – we only need the latest chosen set. Storing once avoids write-amplification and eases querying.
- **What about memory leaks from streamed React nodes?**
  The `assistant-ui` library handles unmounting. No action required.
- **Can older conversations migrate automatically?**
  Yes – a background job can read the last `AiMessage` that contains `CONTEXT:\n…` and backfill `AiConversation.metadata.contextIds`.

---

## Cut-over Checklist

- [ ] Migration applied in all environments
- [ ] `/api/aipane/assistant` route deployed
- [ ] Frontend ChatV2 behind feature flag `ENABLE_CHAT_V2`
- [ ] Post-launch: remove v1 specific code after 2 weeks of stability

---

## Appendix A — Type Imports

```ts
import { CoreMessage } from 'ai'
import type { AIPaneChatRequest } from '@/app/api/aipane/assistant/types'
```

## Appendix B — Directory Snapshot

```
app/
└─ api/
   └─ aipane/
      └─ assistant/
         ├─ route.ts
         ├─ types.ts
         ├─ config.ts
         └─ README.md
└─ (conv)/aipane/
   └─ components/
      └─ ChatV2.tsx
```
