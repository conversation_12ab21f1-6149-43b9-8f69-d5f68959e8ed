import type { ExecutionStepData } from './types'

/**
 * ExecutionStepCollector - Simplified for raw provider data
 * Collects execution steps directly from AI SDK 5 stream chunks
 */
export class ExecutionStepCollector {
  private steps: ExecutionStepData[] = []
  private currentStepOrder: number = 0

  /**
   * Adds a step with raw provider data
   * This is the main method for AI SDK 5 integration
   */
  addStep(stepData: {
    providerType: string // Raw provider type from AI SDK
    metadata: { raw: any; [key: string]: any } // Raw step object + any additional metadata
    parallelKey?: string
    parentStepId?: string
  }): void {
    this.steps.push({
      stepOrder: this.currentStepOrder++,
      type: stepData.providerType,
      metadata: stepData.metadata,
      parallelKey: stepData.parallelKey,
      parentStepId: stepData.parentStepId,
    })
  }


  /**
   * Returns all collected steps
   */
  getSteps(): ExecutionStepData[] {
    return [...this.steps]
  }

  /**
   * Returns steps grouped by provider type for analytics
   */
  getStepsByType(): Record<string, ExecutionStepData[]> {
    return this.steps.reduce(
      (acc, step) => {
        if (!acc[step.type]) {
          acc[step.type] = []
        }
        acc[step.type].push(step)
        return acc
      },
      {} as Record<string, ExecutionStepData[]>
    )
  }

  /**
   * Clears all collected steps
   */
  clear(): void {
    this.steps = []
    this.currentStepOrder = 0
  }

  /**
   * Returns the current step count
   */
  getStepCount(): number {
    return this.steps.length
  }
}
