/**
 * Utility functions for AIPane Assistant API V2
 * Simplified context management and message building
 */

import type { ModelMessage } from 'ai'
import prisma from '@/app/libs/prismadb'
import { getCachedMessages } from '@/libs/cache/conversation-cache'
import type { ContextResult } from './types'
import { CONTEXT_CONFIG, SYSTEM_PROMPT } from './config'

/**
 * Build context messages using simplified approach:
 * - Max 7 assistant messages (14 total)
 * - 4000 char input limit (already validated)
 * - Single system prompt + context
 */
export async function buildContextMessagesV2(
  conversationId: string,
  latestUserMessage: ModelMessage,
  contextIds?: string[]
): Promise<ContextResult> {
  try {
    // Step 1: Get conversation history from cache or database
    let conversationMessages: Array<{
      id: string
      role: 'USER' | 'ASSISTANT' | 'SYSTEM'
      content: string
      createdAt: Date
    }> = []

    // Try cache first
    const cachedMessages = getCachedMessages(conversationId)
    if (cachedMessages) {
      conversationMessages = cachedMessages.map(msg => ({
        id: msg.id,
        role: msg.role.toUpperCase() as 'USER' | 'ASSISTANT' | 'SYSTEM',
        content: msg.content,
        createdAt:
          typeof msg.createdAt === 'string'
            ? new Date(msg.createdAt)
            : msg.createdAt,
      }))
    } else {
      // Fallback to database
      const dbMessages = await prisma.aiMessage.findMany({
        where: { conversationId },
        orderBy: { createdAt: 'asc' },
        select: {
          id: true,
          role: true,
          content: true,
          createdAt: true,
        },
      })

      conversationMessages = dbMessages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        createdAt: msg.createdAt,
      }))
    }

    // Step 2: Apply message limits (keep last 7 assistant messages max)
    const assistantMessages = conversationMessages.filter(
      msg => msg.role === 'ASSISTANT'
    )
    let truncated = false

    if (assistantMessages.length > CONTEXT_CONFIG.MAX_ASSISTANT_MESSAGES) {
      // Find the cutoff point - keep messages after the Nth assistant message
      const cutoffAssistantMessage =
        assistantMessages[
          assistantMessages.length - CONTEXT_CONFIG.MAX_ASSISTANT_MESSAGES
        ]
      const cutoffIndex = conversationMessages.findIndex(
        msg => msg.id === cutoffAssistantMessage.id
      )

      conversationMessages = conversationMessages.slice(cutoffIndex)
      truncated = true
    }

    // Step 3: Get context text from conversation metadata or contextIds
    let contextText = ''
    let finalContextIds: string[] = []

    if (contextIds && contextIds.length > 0) {
      // New conversation with context provided
      finalContextIds = contextIds
      contextText = await buildContextText(contextIds)
    } else {
      // Existing conversation - get context from metadata
      const conversation = await prisma.aiConversation.findUnique({
        where: { id: conversationId },
        select: { metadata: true },
      })

      if (conversation?.metadata && typeof conversation.metadata === 'object') {
        const metadata = conversation.metadata as { contextIds?: string[] }
        if (metadata.contextIds) {
          finalContextIds = metadata.contextIds
          contextText = await buildContextText(metadata.contextIds)
        }
      }
    }

    // Step 4: Build final message array
    const messages: ModelMessage[] = []

    // System prompt (with context if available)
    let systemContent = SYSTEM_PROMPT
    if (contextText) {
      systemContent += `\n\nWe did some quick researches, below are the findings:\n${contextText}`
    }

    messages.push({
      role: 'system',
      content: systemContent,
    })

    // Historical messages (excluding system messages to avoid duplication)
    const historyMessages = conversationMessages
      .filter(msg => msg.role !== 'SYSTEM')
      .map(msg => ({
        role: msg.role.toLowerCase() as 'user' | 'assistant',
        content: msg.content,
      }))

    messages.push(...historyMessages)

    // Latest user message
    messages.push(latestUserMessage)

    return {
      success: true,
      data: {
        messages,
        contextText: contextText || undefined,
        contextIds: finalContextIds.length > 0 ? finalContextIds : undefined,
        truncated,
      },
    }
  } catch (error) {
    console.error('Error building context messages:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

/**
 * Build context text from document IDs
 * TODO: Connect to real document store - this is currently a placeholder
 * In production, this should fetch actual document content from your document store
 */
async function buildContextText(contextIds: string[]): Promise<string> {
  try {
    // TODO: Replace this placeholder implementation with real document fetching
    // Expected behavior:
    // 1. Query document store/database with contextIds
    // 2. Retrieve document content (text, metadata)
    // 3. Format content for AI context (e.g., markdown, structured text)
    // 4. Apply length limits and summarization if needed
    if (contextIds.length === 0) return ''

    return `Context documents: ${contextIds.join(', ')}\n[Document content would be fetched and inserted here]`
  } catch (error) {
    console.error('Error building context text:', error)
    return ''
  }
}

/**
 * Validate input message length
 */
export function validateMessageLength(content: string): boolean {
  return content.length <= CONTEXT_CONFIG.MAX_INPUT_CHARS
}

/**
 * Truncate message if too long
 */
export function truncateMessage(content: string): string {
  if (content.length <= CONTEXT_CONFIG.MAX_INPUT_CHARS) {
    return content
  }

  return content.substring(0, CONTEXT_CONFIG.MAX_INPUT_CHARS - 3) + '...'
}
