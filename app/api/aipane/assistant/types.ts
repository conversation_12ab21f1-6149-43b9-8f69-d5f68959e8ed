/**
 * Type definitions for AIPane Assistant API V2
 * Using CoreMessage from AI SDK for assistant-ui compatibility
 */

import type { ModelMessage } from 'ai'

// ─────────────────────────────────────────────
// Request Types
// ─────────────────────────────────────────────

/**
 * Chat request payload for assistant-ui integration
 */
export type AIPaneChatRequest = {
  /** Messages array compatible with assistant-ui CoreMessage format */
  messages: ModelMessage[]
  /** Conversation ID (required, must start with 'thread_') */
  conversationId: string
  /** Optional metadata (only provided on first request for new conversations) */
  metadata?: {
    /** Context document IDs to include in the conversation */
    contextIds?: string[]
  }
  /** Model to use (defaults to gpt-4.1) */
  model?: string
  /** Additional settings for the AI model */
  settings?: Record<string, unknown>
}

// ─────────────────────────────────────────────
// Response Types
// ─────────────────────────────────────────────

/**
 * Streaming data chunk types for real-time updates
 */
export type StreamingDataChunk =
  | {
      type: 'stream-start'
      conversationId: string
      timestamp: number
    }
  | {
      type: 'execution-step'
      step: {
        type: 'TOOL_CALL' | 'TOOL_RESULT'
        toolName: string
        args?: Record<string, any>
        result?: any
        toolCallId?: string
        timestamp: number
      }
    }
  | {
      type: 'stream-finish'
      timestamp: number
    }

// ─────────────────────────────────────────────
// Context Types
// ─────────────────────────────────────────────

/**
 * Context data for conversation
 */
export type ConversationContext = {
  /** Document IDs included in this conversation */
  contextIds: string[]
  /** Generated context text from documents */
  contextText: string
}

// ─────────────────────────────────────────────
// Metadata Types
// ─────────────────────────────────────────────

/**
 * Conversation-level metadata stored in AiConversation.metadata
 */
export type ConversationMetadata = {
  /** Document IDs selected for this conversation */
  contextIds?: string[]
  /** Additional conversation settings */
  settings?: Record<string, unknown>
}

/**
 * Message-level metadata stored in AiMessage.metadata
 */
export type MessageMetadata = {
  /** Whether the message generation completed successfully */
  runCompleted?: boolean
  /** Token usage statistics */
  tokenUsage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
  /** Model finish reason */
  finishReason?: string
  /** Number of execution steps */
  stepCount?: number
  /** Tool usage information */
  toolsUsed?: string[]
}

// ─────────────────────────────────────────────
// Validation Types
// ─────────────────────────────────────────────

/**
 * Input validation result
 */
export type ValidationResult = {
  isValid: boolean
  error?: string
  truncated?: boolean
}

// ─────────────────────────────────────────────
// Service Types
// ─────────────────────────────────────────────

/**
 * Service operation result
 */
export type ServiceResult<T> = {
  success: boolean
  data?: T
  error?: string
}

/**
 * Context management service result
 */
export type ContextResult = ServiceResult<{
  messages: ModelMessage[]
  contextText?: string
  contextIds?: string[]
  truncated: boolean
}>

/**
 * Message persistence result
 */
export type PersistenceResult = ServiceResult<{
  conversationId: string
  userMessageId: string
  assistantMessageId: string
}>
