/**
 * Configuration constants for AIPane Assistant API V2
 * Centralized constants following the simplified context management approach
 */

// ─────────────────────────────────────────────
// Rate Limiting Configuration
// ─────────────────────────────────────────────
export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 60000, // 1 minute window
  MAX_REQUESTS: 5, // 5 requests per minute
} as const

// ─────────────────────────────────────────────
// Model Configuration
// ─────────────────────────────────────────────
export const MODEL_CONFIG = {
  DEFAULT_MODEL: 'gpt-4.1',
  MAX_STEPS: 10,
  TEMPERATURE: 0.7,
  MAX_TOKENS: 10000, // More generous output token limit
} as const

// ─────────────────────────────────────────────
// Context Management (Simplified)
// ─────────────────────────────────────────────
export const CONTEXT_CONFIG = {
  MAX_INPUT_CHARS: 8000, // Maximum characters per user input (increased for longer messages)
  MAX_ASSISTANT_MESSAGES: 7, // Keep last 7 assistant messages
  MAX_TOTAL_MESSAGES: 15, // Design limit: 7 user + 7 assistant + 1 system message
  // Currently for documentation - actual truncation uses MAX_ASSISTANT_MESSAGES
  // System message contains context and doesn't count toward conversation limits
} as const

// ─────────────────────────────────────────────
// Retry Configuration
// ─────────────────────────────────────────────
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  BACKOFF_MS: 50,
} as const

// ─────────────────────────────────────────────
// Tool Names
// ─────────────────────────────────────────────
export const TOOL_NAMES = {
  WEB_SEARCH: 'web_search',
} as const

// ─────────────────────────────────────────────
// System Prompt
// ─────────────────────────────────────────────
export const SYSTEM_PROMPT = `You are a helpful AI assistant. You have access to web search capabilities to help answer questions with up-to-date information.

When using web search:
1. Use it when you need current information or when the user asks about recent events
2. Search for specific, relevant terms
3. Provide sources for the information you find
4. Synthesize information from multiple sources when appropriate

Always be helpful, accurate, and cite your sources when using web search results.`

// ─────────────────────────────────────────────
// Error Messages
// ─────────────────────────────────────────────
export const ERROR_MESSAGES = {
  INPUT_TOO_LONG: `Message is too long. Please limit your input to ${CONTEXT_CONFIG.MAX_INPUT_CHARS} characters.`,
  CONVERSATION_TOO_LONG:
    'This conversation has reached the maximum length. Please start a new conversation.',
  INVALID_CONVERSATION_ID:
    'Invalid conversation ID format. Must start with "thread_".',
  MISSING_MESSAGES:
    'Messages array is required and must contain at least one message.',
} as const
