/**
 * Research Generation API Endpoint
 *
 * Main API endpoint for AI-powered research generation with web search integration.
 * Uses Azure OpenAI GPT-4o-mini with Vercel AI SDK tool calling for web search.
 *
 * Key Features:
 * - Language detection from drag tree settings
 * - Brave Search API integration via buildSearchTools
 * - Streaming responses with onFinish callback
 * - Content metadata collection for frontend display
 * - Status management (INITIALIZED → PROCESSING → ACTIVE)
 * - Tool step limiting to ensure final answer generation
 *
 * Request Format Support:
 * - Chat message format (from useChat hook)
 * - Direct format (manual API calls)
 *
 * Critical Implementation Details:
 * - Uses request-scoped metadata collector for thread safety
 * - Reserved steps logic ensures AI provides final answer
 * - Comprehensive error handling with status updates
 */

import { streamText } from 'ai'
import { NextResponse } from 'next/server'
import { azure } from '@ai-sdk/azure'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/app/api/auth/authOptions'
import {
  DragTreeNodeContentStatus,
  Prisma,
  AIUsageType,
  DragTreeNodeContentType,
} from '@prisma/client'
import { createResearchPrompt } from '@/app/api/dragtree/shared/research-prompts'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/(conv)/screening/constants/languages'
import { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
import prisma from '@/app/libs/prismadb'
import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'
import { getModelConfig } from '@/app/libs/model-config'

export const maxDuration = 60

export type ResearchGenerateRequestType = {
  contentId: string
  questionText: string
  researchType?: DragTreeNodeContentType
}

export async function POST(req: Request) {
  try {
    // Get session for authentication
    const session = await getServerSession(authOptions)
    const userId = session?.user?.id

    if (!userId) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    const requestBody = await req.json()

    // Handle both chat message format (from useChat) and direct format
    let contentId: string
    let questionText: string
    let researchType: string = DragTreeNodeContentType.QUICK_RESEARCH

    if (requestBody.messages) {
      // Chat message format (from useChat)
      const userMessage = requestBody.messages.find(
        (msg: any) => msg.role === 'user'
      )
      if (!userMessage) {
        return NextResponse.json(
          { error: 'No user message found in chat format' },
          { status: 400 }
        )
      }

      // Extract data from body parameters alongside messages
      if (requestBody.contentId && requestBody.questionText) {
        contentId = requestBody.contentId
        questionText = requestBody.questionText
        researchType =
          requestBody.researchType || DragTreeNodeContentType.QUICK_RESEARCH
      } else {
        // Try to parse from message content (JSON format)
        try {
          const parsedContent = JSON.parse(userMessage.content)
          contentId = parsedContent.contentId
          questionText =
            parsedContent.questionText ||
            parsedContent.question ||
            userMessage.content
          researchType =
            parsedContent.researchType || DragTreeNodeContentType.QUICK_RESEARCH
        } catch {
          return NextResponse.json(
            { error: 'Missing contentId in chat message format' },
            { status: 400 }
          )
        }
      }
    } else {
      // Direct format
      const directRequest = requestBody as ResearchGenerateRequestType
      contentId = directRequest.contentId
      questionText = directRequest.questionText
      researchType =
        directRequest.researchType || DragTreeNodeContentType.QUICK_RESEARCH
    }

    if (!contentId || !questionText) {
      return NextResponse.json(
        { error: 'Missing required fields: contentId, questionText' },
        { status: 400 }
      )
    }

    console.log(
      `🔬 [Research Generate] Starting research for content: ${contentId}`
    )

    // Fetch the drag tree to get preferred language and screening question
    const nodeContent = await prisma.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      include: {
        drag_tree_node: {
          include: {
            drag_tree: true,
          },
        },
      },
    })

    if (!nodeContent?.drag_tree_node?.drag_tree) {
      throw new Error('Drag tree not found for content')
    }

    const dragTree = nodeContent.drag_tree_node.drag_tree

    console.log(
      `🔍 [Research Generate] Raw preferred_language from DB:`,
      dragTree.preferred_language,
      `(type: ${typeof dragTree.preferred_language})`
    )

    const preferredLanguage =
      (dragTree.preferred_language as SupportedLanguageCode) || 'en'
    const language = getLanguageName(preferredLanguage)
    const screeningQuestion = dragTree.user_prompt || undefined

    console.log(
      `🔍 [Research Generate] After processing: preferredLanguage="${preferredLanguage}", language="${language}"`
    )

    console.log(
      `🌍 [Research Generate] Language detected: ${preferredLanguage} -> "${language}", Screening: ${screeningQuestion ? 'Yes' : 'No'}`
    )
    console.log(
      `📝 [Research Generate] Prompt will end with: "Please generate the language in ${language}"`
    )

    // Get model configuration based on user's subscription tier
    const modelConfig = await getModelConfig(
      userId,
      'dragtree_research_generate'
    )
    const model_name = modelConfig.model

    // --- REFACTORED METADATA HANDLING ---
    // 1. Create a single, authoritative metadata object upfront.
    const generationMetadata = {
      researchType,
      originalQuestion: questionText,
      model: model_name,
      language: preferredLanguage,
      startedAt: new Date().toISOString(),
      completedAt: '', // Will be set on finish
      hasWebSearch: false, // Will be updated on finish
      tokenUsage: {
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
      },
    }

    // Update content status to PROCESSING
    await prisma.dragTreeNodeContent.update({
      where: { id: contentId },
      data: {
        status: DragTreeNodeContentStatus.PROCESSING,
        // Store the initial metadata
        generation_metadata: generationMetadata,
      },
    })

    const systemMessage = createResearchPrompt(
      questionText,
      screeningQuestion,
      language
    )

    console.log(
      `🔍 [Research Generate] Generated prompt preview (last 200 chars):`,
      systemMessage.slice(-200)
    )

    // Request-scoped metadata collector for thread safety
    // Each API request gets its own collector to prevent cross-request contamination
    const collectedSearchMetadata: SearchMetadata[] = []

    // Reserve steps logic to ensure AI provides final answer
    // Without this, AI might use all steps for web search without generating final response
    const MAX_STEPS = 8
    const RESERVED_STEPS = 1 // guarantee at least 1 step for final answer

    // Use Vercel AI SDK streamText with web search tools
    const result = streamText({
      model: azure(model_name),
      messages: [
        {
          role: 'system',
          content: systemMessage,
        },
      ],
      tools: buildSearchTools(collectedSearchMetadata),
      // Note: maxSteps not available in this AI SDK version
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens,
      // @ts-expect-error experimental feature not yet in type defs
      experimental_prepareStep: async ({
        stepNumber,
      }: {
        stepNumber: number
      }) => {
        const remaining = MAX_STEPS - stepNumber
        if (remaining <= RESERVED_STEPS) {
          // Disable tools for the last reserved step(s)
          return {
            toolChoice: 'none',
            // No active tools means model must answer.
            experimental_activeTools: [],
          }
        }
        return undefined
      },
      onFinish: async result => {
        try {
          console.log(
            `✅ [Research Generate] Completed research for content: ${contentId}`
          )

          const searchMetadata = collectedSearchMetadata
          console.log(
            `📊 [Research Generate] Collected ${searchMetadata.length} search metadata entries`
          )

          // --- REFACTORED METADATA HANDLING ---
          // 2. Augment the original metadata object with final details.
          generationMetadata.completedAt = new Date().toISOString()
          generationMetadata.tokenUsage = {
            inputTokens: (result.usage as any)?.promptTokens || 0,
            outputTokens: (result.usage as any)?.completionTokens || 0,
            totalTokens:
              ((result.usage as any)?.promptTokens || 0) +
              ((result.usage as any)?.completionTokens || 0),
          }
          generationMetadata.hasWebSearch = searchMetadata.length > 0

          // 3. Use the single metadata object for both fields.
          const contentMetadata = {
            searchResults: searchMetadata,
            generationInfo: generationMetadata,
          }

          // Store complete conversation history including system prompt for future continuation
          const completeConversation = [
            {
              role: 'system',
              content: systemMessage,
            },
            ...result.response.messages,
          ]

          // Update content and log AI usage in parallel
          await Promise.all([
            prisma.dragTreeNodeContent.update({
              where: { id: contentId },
              data: {
                status: DragTreeNodeContentStatus.ACTIVE,
                content_text: result.text,
                content_metadata: contentMetadata,
                messages:
                  completeConversation as unknown as Prisma.InputJsonValue,
                generation_metadata: generationMetadata, // Overwrite with the completed metadata
              },
            }),
            createAIUsage({
              userId: userId,
              entityType: 'drag_tree_node_content',
              entityId: contentId,
              aiProvider: 'azure_openai',
              modelName: model_name,
              usageType: AIUsageType.NODE_QUICK_RESEARCH,
              inputPrompt: systemMessage,
              messages: completeConversation,
              metadata: {
                tokenUsage: result.usage,
                searchMetadataCount: searchMetadata.length,
                hasWebSearch: searchMetadata.length > 0,
                language: preferredLanguage,
                researchType,
              },
              config: {
                maxSteps: MAX_STEPS,
                reservedSteps: RESERVED_STEPS,
              },
            }),
          ])

          console.log(
            `🎉 [Research Generate] Successfully saved research to DB for content: ${contentId} with ${searchMetadata.length} search metadata entries`
          )
        } catch (error) {
          console.error(`💥 [Research Generate] Error saving to DB:`, error)

          // Mark as failed if DB update fails
          await prisma.dragTreeNodeContent.update({
            where: { id: contentId },
            data: {
              status: DragTreeNodeContentStatus.INACTIVE,
              generation_metadata: {
                error: error instanceof Error ? error.message : 'Unknown error',
                failedAt: new Date().toISOString(),
              },
            },
          })
        }
      },
    })

    return result.toUIMessageStreamResponse()
  } catch (error) {
    console.error('💥 [Research Generate] API error:', error)

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
