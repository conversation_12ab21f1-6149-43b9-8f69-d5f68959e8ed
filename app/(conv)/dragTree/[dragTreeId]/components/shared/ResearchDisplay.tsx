import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  ErrorInfo,
  ReactNode,
  useMemo,
} from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Eye, EyeOff, AlertTriangle, ExternalLink } from 'lucide-react'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useResearchLifecycle } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useResearchLifecycle'
import { cn } from '@/lib/utils'
import debounce from 'lodash/debounce'
import { useUIStore } from '@/app/stores/ui_store'
import { motion, AnimatePresence } from 'framer-motion'
import {
  SearchProgressIndicator,
  SourceCitations,
  hasSearchResults,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import TiptapQuickResearchEditor from '@/app/components/editor/TiptapQuickResearchEditor'
import { JSONContent } from '@tiptap/react'
import { shallow } from 'zustand/shallow'

// Simple error boundary for research operations
class ResearchErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[Research] Error boundary caught error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Research component error</span>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

type ResearchDisplayProps = {
  nodeId: string
  questionText: string
  contentId?: string
  className?: string
  enableHoverPreview?: boolean // Control whether to show on general hover
  /**
   * When true, the content will be constrained to a specific height and enable scrolling.
   * Used in tab view or other contexts where height should be limited.
   */
  constrainHeight?: boolean
}

const ResearchDisplay: React.FC<ResearchDisplayProps> = ({
  nodeId,
  questionText,
  contentId,
  className = '',
  enableHoverPreview = false,
  constrainHeight = false,
}) => {
  // --- STATE MANAGEMENT ---
  const nodeContentMap = useDragTreeStore(
    state => state.nodeContent.get(nodeId),
    shallow
  )
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)
  const fetchNodeContent = useDragTreeStore(state => state.fetchNodeContent)
  const getNodePath = useDragTreeStore(state => state.getNodePath)

  const { setHoverCollapseLock } = useUIStore()
  // Removed researchPreviewNodeId to fix shared state and rerender issues
  const { addTab } = useTabStore()

  // --- DERIVED STATE & LIFECYCLE ---
  const fullQuestionPath = React.useMemo(
    () => getNodePath(nodeId) || questionText,
    [nodeId, questionText, getNodePath]
  )

  const { isStreaming, streamingContent, startResearch, activeContentId } =
    useResearchLifecycle({
      nodeId,
      questionText: fullQuestionPath,
    })

  // Memoize all heavy derived computations
  const { workingContentId, status, storeContent, searchResults } =
    useMemo(() => {
      const workingContentIdLocal =
        contentId ||
        activeContentId ||
        (nodeContentMap && nodeContentMap.size > 0
          ? Array.from(nodeContentMap.keys())[0]
          : null)

      const storedNodeContentLocal = workingContentIdLocal
        ? nodeContentMap?.get(workingContentIdLocal)
        : null

      const statusLocal = storedNodeContentLocal?.status || null
      const storeContentLocal = storedNodeContentLocal?.contentText || ''

      const searchResultsLocal =
        storedNodeContentLocal?.metadata &&
        hasSearchResults(storedNodeContentLocal.metadata)
          ? storedNodeContentLocal.metadata.searchResults
          : null

      return {
        workingContentId: workingContentIdLocal,
        storedNodeContent: storedNodeContentLocal,
        status: statusLocal,
        storeContent: storeContentLocal,
        searchResults: searchResultsLocal,
      }
    }, [contentId, activeContentId, nodeContentMap])

  // --- LOCAL UI STATE ---
  // Start with content visible if status is PROCESSING or there's already content
  const [showContent, setShowContent] = useState<boolean>(
    status === DragTreeNodeContentStatus.PROCESSING ||
      (status === DragTreeNodeContentStatus.ACTIVE &&
        storeContent.trim().length > 0)
  )
  const [isPersistent, setIsPersistent] = useState<boolean>(false)
  const [isHovered, setIsHovered] = useState<boolean>(false)
  const [isEditable, setIsEditable] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [justSaved, setJustSaved] = useState<boolean>(false)
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('')
  const [isSearching, setIsSearching] = useState<boolean>(false)

  // Local state for user-editable content. Synced from the store or stream.
  const [localContent, setLocalContent] = useState<string>(storeContent)

  // This effect ensures localContent is updated from the store when not streaming.
  useEffect(() => {
    // When streaming, the content is driven by the stream.
    // When not streaming, sync with the store's version.
    if (!isStreaming) {
      setLocalContent(storeContent)
    }
  }, [storeContent, isStreaming])

  // Auto-start research when content is in INITIALIZED status
  useEffect(() => {
    if (status === DragTreeNodeContentStatus.INITIALIZED && workingContentId) {
      // Small delay to ensure store is fully updated
      setTimeout(() => {
        startResearch(workingContentId) // Pass the existing content ID
      }, 100)
    }
  }, [status, workingContentId, startResearch])

  // Keep localContent in sync with streamingContent during streaming
  useEffect(() => {
    if (isStreaming && streamingContent) {
      setLocalContent(streamingContent)
    }
  }, [isStreaming, streamingContent])

  // Lazy-load content when user tries to view but content missing
  useEffect(() => {
    if (
      showContent &&
      status === DragTreeNodeContentStatus.ACTIVE &&
      storeContent.trim().length === 0 &&
      workingContentId
    ) {
      fetchNodeContent(nodeId, workingContentId)
    }
  }, [
    showContent,
    status,
    storeContent,
    workingContentId,
    nodeId,
    fetchNodeContent,
  ])

  // Track whether the current session is/was streaming so we can detect the
  // precise moment a stream finishes.
  const wasStreamingRef = useRef<boolean>(false)

  // Mark that we are streaming when `isStreaming` flips to true.
  useEffect(() => {
    if (isStreaming) {
      wasStreamingRef.current = true
    }
  }, [isStreaming])

  // When streaming ends AND the status is COMPLETED, auto-pin the block so it
  // remains visible. This only runs immediately after a successful stream,
  // leaving pre-existing completed research untouched (user needs to hover or
  // click as before).
  useEffect(() => {
    if (
      !isStreaming &&
      wasStreamingRef.current &&
      status === DragTreeNodeContentStatus.ACTIVE
    ) {
      setIsPersistent(true)
      setShowContent(true)
      setHoverCollapseLock(true)

      // reset flag so future loads don't retrigger
      wasStreamingRef.current = false
    }
  }, [isStreaming, status, setHoverCollapseLock])

  // Refs for timeouts
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const justSavedTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Prioritize streaming content if active
  const actualContent =
    isStreaming && streamingContent ? streamingContent : storeContent

  const debouncedSave = useCallback(
    debounce((newContentId: string, newContentText: string) => {
      updateNodeContent(nodeId, newContentId, { contentText: newContentText })
      setIsSaving(false)
      setJustSaved(true)
      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
      justSavedTimeoutRef.current = setTimeout(() => {
        setJustSaved(false)
      }, 2000)
    }, 1000),
    [nodeId, updateNodeContent]
  )

  // --- HOOKS (SIDE EFFECTS) ---

  useEffect(() => {
    // Sync local state with external content (from store or streaming).
    if (actualContent !== localContent) {
      setLocalContent(actualContent)
    }
  }, [actualContent])

  useEffect(() => {
    if (isPersistent) {
      setShowContent(true)
    }
  }, [isPersistent])

  // Consolidated visibility logic - this is the master effect that controls showContent
  useEffect(() => {
    // Priority 1: Always show during streaming, processing, or initializing
    if (
      isStreaming ||
      status === DragTreeNodeContentStatus.PROCESSING ||
      status === DragTreeNodeContentStatus.INITIALIZED
    ) {
      setShowContent(true)
      return
    }

    // Priority 2: Show if persistent (user pinned it)
    if (isPersistent) {
      setShowContent(true)
      return
    }

    // Priority 3: Hide if not persistent and not streaming/processing
    setShowContent(false)
  }, [isStreaming, isPersistent, status])

  // Force show content when streaming starts
  useEffect(() => {
    if (isStreaming) {
      setShowContent(true)
      console.log(
        '🔬 [ResearchDisplay] Streaming started, showing content area'
      )
    }
  }, [isStreaming])

  // Removed global research preview effect to prevent shared state issues
  // Each component now manages its own state independently

  // Monitor streaming for search activity - improved detection
  useEffect(() => {
    if (isStreaming) {
      // Check if streaming content indicates search activity
      if (streamingContent) {
        // Look for search patterns in the streaming content
        const searchPatterns = [
          /searching.*?for.*?"([^"]+)"/i,
          /searching.*?"([^"]+)"/i,
          /search.*?"([^"]+)"/i,
          /web search.*?"([^"]+)"/i,
        ]

        let foundQuery = null
        for (const pattern of searchPatterns) {
          const match = streamingContent.match(pattern)
          if (match && match[1]) {
            foundQuery = match[1]
            break
          }
        }

        if (foundQuery) {
          setCurrentSearchQuery(foundQuery)
          setIsSearching(true)
        } else if (
          streamingContent.includes('found') ||
          streamingContent.includes('results')
        ) {
          setIsSearching(false)
        }
      }
    } else {
      // Clear search state when streaming ends
      setIsSearching(false)
      setCurrentSearchQuery('')
    }
  }, [isStreaming, streamingContent])

  // Effect to release lock on unmount
  useEffect(() => {
    return () => {
      if (isPersistent) {
        setHoverCollapseLock(false)
      }
      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
    }
  }, [isPersistent, setHoverCollapseLock])

  // --- EVENT HANDLERS & RENDER LOGIC ---

  const handleContentChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>
  ) => {
    const newContent = event.target.value
    setLocalContent(newContent)

    if (workingContentId) {
      setIsSaving(true)
      debouncedSave(workingContentId, newContent)
    }
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current)
    // Show content only if enableHoverPreview is true (removed global preview dependency)
    if (enableHoverPreview && !isPersistent && !isStreaming) {
      setShowContent(true)
      setHoverCollapseLock(true)
    }
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
    // Hide content only if enableHoverPreview is true (removed global preview dependency)
    if (
      enableHoverPreview &&
      !isPersistent &&
      status !== DragTreeNodeContentStatus.PROCESSING
    ) {
      hideTimeoutRef.current = setTimeout(() => {
        setShowContent(false)
        setHoverCollapseLock(false)
      }, 300)
    }
  }

  // Handle button-specific hover to show content immediately
  const handleButtonMouseEnter = () => {
    // Clear any pending hide timeout
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current)

    // Show content immediately when hovering over the button if there's existing content
    if (
      nodeContentMap &&
      nodeContentMap.size > 0 &&
      !isPersistent &&
      !isStreaming
    ) {
      setShowContent(true)
      setHoverCollapseLock(true)
    }
  }

  const handleButtonMouseLeave = () => {
    // Only hide if not persistent and not currently in the content area
    if (
      !isPersistent &&
      !isHovered &&
      status !== DragTreeNodeContentStatus.PROCESSING
    ) {
      hideTimeoutRef.current = setTimeout(() => {
        setShowContent(false)
        setHoverCollapseLock(false)
      }, 300)
    }
  }

  const handleClick = async (event: React.MouseEvent) => {
    // Prevent the click from bubbling up to parent nodes (which could trigger collapse)
    event.stopPropagation()
    event.preventDefault()

    if (nodeContentMap && nodeContentMap.size > 0) {
      const nextIsPersistent = !isPersistent
      setIsPersistent(nextIsPersistent)
      setShowContent(nextIsPersistent)
      setHoverCollapseLock(nextIsPersistent)
    } else {
      // Starting research: Just kick off the process. The effects will
      // handle showing the content and pinning it upon completion.
      await startResearch()
    }
  }

  const getPlaceholderText = () => {
    switch (status) {
      case DragTreeNodeContentStatus.PROCESSING:
        return 'Generating research content...'
      case DragTreeNodeContentStatus.INITIALIZED:
        return 'Research starting...'
      case null:
        return 'Click the research button to start.'
      default:
        return 'Research content'
    }
  }

  const handleTextareaClick = (event: React.MouseEvent) => {
    // Prevent click from bubbling to parent nodes
    event.stopPropagation()

    if (!isEditable) {
      setIsEditable(true)
    }
    if (!isPersistent) {
      setIsPersistent(true)
      setShowContent(true)
      setHoverCollapseLock(true)
    }
  }

  const handleOpenInTab = () => {
    if (localContent.trim() && workingContentId) {
      // Extract just the question part from the full path
      const pathParts = fullQuestionPath.split(' > ')
      const questionOnly = pathParts[pathParts.length - 1] || fullQuestionPath

      const tabTitle =
        questionOnly.length > 30
          ? `${questionOnly.substring(0, 30)}...`
          : questionOnly

      addTab({
        title: tabTitle,
        fullTitle: fullQuestionPath,
        type: 'research',
        nodeId,
        contentId: workingContentId,
        isClosable: true,
      })
    }
  }

  // --- CONDITIONAL RENDER (The final step) ---
  if (!nodeContentMap) {
    return null
  }

  return (
    <div
      className={cn('w-full max-w-full overflow-x-hidden', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Subtle Research Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClick}
            onMouseEnter={handleButtonMouseEnter}
            onMouseLeave={handleButtonMouseLeave}
            className={cn(
              'h-6 px-2 text-xs font-normal transition-all duration-200 border-0',
              {
                'bg-blue-50/80 text-blue-600 shadow-sm':
                  showContent || isHovered,
                'bg-gray-50/60 text-gray-500 hover:bg-gray-100/80 hover:text-gray-700':
                  !showContent && !isHovered,
              }
            )}
          >
            {isPersistent ? (
              <EyeOff className="h-3 w-3 mr-1" />
            ) : (
              <Eye className="h-3 w-3 mr-1" />
            )}
            <span className="text-xs">
              {isStreaming || status === DragTreeNodeContentStatus.PROCESSING
                ? 'Researching...'
                : status === DragTreeNodeContentStatus.ACTIVE
                  ? 'Quick Research'
                  : 'Quick Research'}
            </span>
            {status === DragTreeNodeContentStatus.ACTIVE && !isStreaming && (
              <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-green-500"></span>
            )}
            {(status === DragTreeNodeContentStatus.PROCESSING ||
              isStreaming) && (
              <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
            )}
          </Button>

          {/* Source Citations in tree view cards */}
          {searchResults &&
            searchResults.length > 0 &&
            status === DragTreeNodeContentStatus.ACTIVE && (
              <div className="flex-shrink-0">
                <SourceCitations
                  searchResults={searchResults}
                  maxSources={searchResults.length}
                  className=""
                />
              </div>
            )}
        </div>

        {/* Open in Tab Button - aligned right */}
        {status === DragTreeNodeContentStatus.ACTIVE && localContent.trim() && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleOpenInTab}
            className={cn(
              'h-6 px-2 text-xs font-normal border-0',
              'bg-gray-50/60 text-gray-500 hover:bg-gray-100/80 hover:text-gray-700'
            )}
            title="Open in separate tab"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Search Progress Indicator */}
      {isSearching && (
        <div className="mt-2">
          <SearchProgressIndicator
            isSearching={isSearching}
            currentQuery={currentSearchQuery}
          />
        </div>
      )}

      {/* Animated Content */}
      <div
        className={cn(
          'transition-all duration-500 ease-in-out overflow-hidden',
          'w-full max-w-full',
          {
            'max-h-0 opacity-0 mt-0': !showContent,
            // Use different max heights based on constrainHeight prop
            [constrainHeight
              ? 'max-h-[400px] opacity-100 mt-3'
              : 'max-h-[1000px] opacity-100 mt-3']: showContent,
          }
        )}
        style={{
          width: '100%',
          maxWidth: '100%',
          minWidth: 0,
          boxSizing: 'border-box',
        }}
      >
        <div
          className={cn(
            'relative w-full max-w-full',
            constrainHeight ? 'h-full' : ''
          )}
          style={{
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            boxSizing: 'border-box',
            overflow: 'hidden',
            ...(constrainHeight ? { height: '350px' } : {}),
          }}
        >
          {/* Show textarea during streaming, TiptapQuickResearchEditor when complete */}
          {isStreaming ||
          status === DragTreeNodeContentStatus.PROCESSING ||
          status === DragTreeNodeContentStatus.INITIALIZED ? (
            <Textarea
              value={
                isStreaming && streamingContent
                  ? streamingContent
                  : localContent
              }
              onChange={handleContentChange}
              onMouseDown={e => e.stopPropagation()}
              onKeyDown={e => e.stopPropagation()}
              onClick={handleTextareaClick}
              placeholder={getPlaceholderText()}
              readOnly={!isEditable || isStreaming}
              className={cn(
                'w-full text-sm transition-all duration-300 resize-none',
                constrainHeight ? 'h-full' : 'min-h-[200px]',
                isEditable && !isStreaming
                  ? 'bg-white border-blue-200 focus:border-blue-400'
                  : 'bg-gray-50 border-gray-200'
              )}
              rows={constrainHeight ? undefined : 8}
              style={constrainHeight ? { height: '100%' } : {}}
            />
          ) : (
            <TiptapQuickResearchEditor
              content={localContent || ''}
              onContentChange={(newContent: string) => {
                setLocalContent(newContent)
                if (workingContentId) {
                  setIsSaving(true)
                  debouncedSave(workingContentId, newContent)
                }
              }}
              onJSONChange={(jsonContent: JSONContent) => {
                const jsonString = JSON.stringify(jsonContent)
                setLocalContent(jsonString)
                if (workingContentId) {
                  setIsSaving(true)
                  debouncedSave(workingContentId, jsonString)
                }
              }}
              isReadOnly={false}
              isStreaming={false}
              placeholder={getPlaceholderText()}
              onClick={handleTextareaClick}
              className="max-w-full"
              showBubbleMenu={!constrainHeight}
              debounceMs={1000}
              autoFocus={false}
              compact={!constrainHeight}
              questionText={questionText}
              questionNodeId={nodeId}
              showResearchButton={false}
            />
          )}
        </div>

        {/* Status Footer - only show for textarea (during streaming), TiptapQuickResearchEditor has its own */}
        {(isStreaming ||
          status === DragTreeNodeContentStatus.PROCESSING ||
          status === DragTreeNodeContentStatus.INITIALIZED) && (
          <div className="flex items-center justify-end text-xs text-gray-400 h-5 pr-2">
            <AnimatePresence mode="wait">
              {isSaving ? (
                <motion.div
                  key="saving"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <span className="mr-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
                  Saving...
                </motion.div>
              ) : justSaved ? (
                <motion.div
                  key="saved"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                  style={{ color: '#10b981' }}
                >
                  Saved
                </motion.div>
              ) : !isEditable && !isStreaming && localContent ? (
                <motion.div
                  key="edit"
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -5 }}
                >
                  Click to edit
                </motion.div>
              ) : null}
            </AnimatePresence>
          </div>
        )}
      </div>
    </div>
  )
}

// Wrap with error boundary for better error handling
const ResearchDisplayWithErrorBoundary: React.FC<
  ResearchDisplayProps
> = props => (
  <ResearchErrorBoundary>
    <ResearchDisplay {...props} />
  </ResearchErrorBoundary>
)

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(ResearchDisplayWithErrorBoundary)
