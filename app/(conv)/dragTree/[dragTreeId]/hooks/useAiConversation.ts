'use client'

import { useState, useCallback, useRef, useEffect, useMemo } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { nanoid } from 'nanoid'
import type { Msg } from '@/app/types/ai-sdk5'
import {
  isExecutionStepDataPart,
  isStreamStartDataPart,
  isStreamFinishDataPart,
  isTitleDataPart,
  getDataParts,
  getLatestDataPart,
} from '@/app/types/ai-sdk5'
import {
  getCachedMessages,
  getCachedConversation,
  setCachedMessages,
  type CachedMessage,
  type CachedConversation,
} from '@/app/libs/conversationCache'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

type ConversationMessage = {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  createdAt: string
  stepCount?: number
  isStreaming?: boolean
}

type ConversationData = {
  id: string
  title?: string
  contextEntityType?: string
  contextEntityId?: string
  createdAt: string
  userId: string
  messages: ConversationMessage[]
}

type PaginationInfo = {
  hasMore: boolean
  nextCursor?: string
  limit: number
  total: number
}

type UseAiConversationOptions = {
  // Existing conversation ID to load
  conversationId?: string
  // API endpoint for chat
  apiEndpoint: string
  // Model and settings
  model?: string
  context?: string
  settings?: any
  // Pagination settings
  initialLimit?: number
  // Context for new conversations
  contextEntityType?: string
  contextEntityId?: string
  contextIds?: string[]
  // Tab ID for title updates
  tabId?: string
  // Enable/disable the hook (useful for preventing race conditions)
  enabled?: boolean
}

type UseAiConversationReturn = {
  // Conversation data
  conversation: ConversationData | null
  isLoadingConversation: boolean
  conversationError: string | null

  // Messages with pagination
  messages: ConversationMessage[]
  hasMoreMessages: boolean
  isLoadingMoreMessages: boolean
  loadMoreMessages: () => Promise<void>

  // Chat functionality (from useChat)
  input: string
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  handleSubmit: (e: React.FormEvent) => void
  isLoading: boolean
  append: (message: { role: 'user' | 'assistant'; content: string }) => void

  // Utilities
  refreshConversation: () => Promise<void>
  scrollToMessage: (messageId: string) => void

  // Real-time step streaming
  liveSteps: any[]
  isStreamingSteps: boolean
}

export function useAiConversation(
  options: UseAiConversationOptions
): UseAiConversationReturn {
  const {
    conversationId: initialConversationId,
    apiEndpoint,
    model = 'gpt-4.1',
    context,
    settings = {},
    initialLimit = 50,
    contextEntityType,
    contextEntityId,
    contextIds,
    tabId,
    enabled = true,
  } = options

  // Conversation state
  const [conversation, setConversation] = useState<ConversationData | null>(
    null
  )
  const [isLoadingConversation, setIsLoadingConversation] =
    useState<boolean>(false)
  const [conversationError, setConversationError] = useState<string | null>(
    null
  )

  // Pagination state
  const [paginationInfo, setPaginationInfo] = useState<PaginationInfo>({
    hasMore: false,
    limit: initialLimit,
    total: 0,
  })
  const [isLoadingMoreMessages, setIsLoadingMoreMessages] =
    useState<boolean>(false)

  // Persisted messages from API (older messages)
  const [persistedMessages, setPersistedMessages] = useState<
    ConversationMessage[]
  >(() => {
    if (initialConversationId) {
      const cached = getCachedMessages(initialConversationId)
      if (cached) {
        return cached as any
      }
    }
    return []
  })

  // Current conversation ID
  const [conversationId, setConversationId] = useState<string | undefined>(
    initialConversationId
  )

  // Generate chat key based on conversationId when available
  // This ensures useChat remounts with the correct conversation context
  // and prevents stale cache entries with undefined conversationId
  const chatKey = useMemo(() => {
    if (conversationId) {
      return `chat-${conversationId}-${apiEndpoint.includes('chat-simulator') ? 'sim' : 'real'}`
    }
    // Return undefined to skip useChat initialization until conversationId is available
    return undefined
  }, [conversationId, apiEndpoint])

  // State for real-time step updates
  const [liveSteps, setLiveSteps] = useState<any[]>([])
  const [isStreamingSteps, setIsStreamingSteps] = useState(false)

  // AbortController for fetch operations cleanup
  const abortControllerRef = useRef<AbortController | null>(null)

  // Track component mount state to prevent operations on unmounted components
  const isMountedRef = useRef(true)

  // Only initialize useChat when enabled and conversationId is available
  const shouldInitializeChat =
    enabled && Boolean(conversationId) && Boolean(chatKey)

  const chat = useChat<Msg>(
    !shouldInitializeChat
      ? undefined // Do not mount the hook until the conversation row exists
      : {
          transport: new DefaultChatTransport({
            api: apiEndpoint,
            body: {
              model,
              context,
              settings,
              conversationId, // Pass conversation ID for persistence
              contextEntityType,
              contextEntityId,
              contextIds, // Pass context IDs for preservation
            },
          }),
          id: chatKey!,
          // AI SDK 5 performance optimizations
          experimental_throttle: 16, // ~60fps for smooth UI updates
          onFinish: () => {
            // Clear live steps when streaming finishes
            setLiveSteps([])
            setIsStreamingSteps(false)
            
            // AI SDK 5 optimization: Clear old message parts from memory
            if (messages.length > 100) {
              console.log('🧹 [useAiConversation] Cleaning up old message parts for memory optimization')
            }
          },
        }
  )

  // Handle streaming data for real-time step updates using AI SDK 5 data parts
  useEffect(() => {
    if (chat.messages && chat.messages.length > 0) {
      const lastMessage = chat.messages[chat.messages.length - 1]
      if (lastMessage && lastMessage.parts) {
        // Process all data parts in the latest message
        for (const part of lastMessage.parts) {
          if (isStreamStartDataPart(part)) {
            setIsStreamingSteps(true)
            setLiveSteps([])
          } else if (isExecutionStepDataPart(part)) {
            // Only add tool calls, filter out tool results
            if (part.data.step.type === 'TOOL_CALL') {
              setLiveSteps(prev => [...prev, part.data.step])
            }
          } else if (isStreamFinishDataPart(part)) {
            setIsStreamingSteps(false)
          } else if (isTitleDataPart(part) && tabId) {
            // Update tab title when received from backend
            const { updateTabTitle } = useTabStore.getState()
            const title = part.data.title || 'Chat'
            updateTabTitle(tabId, title)
          }
        }
      }
    }
  }, [chat.messages, tabId])

  // Load conversation data with performance monitoring
  const loadConversation = useCallback(
    async (convId: string, limit: number = initialLimit, cursor?: string) => {
      const startTime = performance.now()

      // Check if component is still mounted before proceeding
      if (!isMountedRef.current) {
        console.log('🚫 [useAiConversation] Component unmounted, skipping load')
        return
      }

      if (!convId || !convId.startsWith('thread_')) {
        console.warn('🔍 [useAiConversation] Invalid conversation ID:', convId)
        return
      }

      console.log(
        `🔍 [useAiConversation] Loading conversation: ${convId}, limit: ${limit}, cursor: ${cursor || 'none'}`
      )

      // Check cache first for initial loads (not pagination)
      if (!cursor) {
        const cachedMessages = getCachedMessages(convId)
        const cachedConversation = getCachedConversation(convId)

        if (cachedMessages && cachedMessages.length > 0) {
          console.log(
            `💾 [useAiConversation] Using cached data for conversation: ${convId} (${cachedMessages.length} messages)`
          )

          // Transform cached messages to match expected format
          const transformedMessages = cachedMessages.map(m => {
            const role = (m.role || '').toLowerCase()
            const validRole = ['user', 'assistant', 'system'].includes(role)
              ? (role as 'user' | 'assistant' | 'system')
              : 'user'
            return {
              ...m,
              role: validRole,
              createdAt:
                typeof m.createdAt === 'string'
                  ? m.createdAt
                  : m.createdAt instanceof Date
                    ? m.createdAt.toISOString()
                    : new Date().toISOString(),
            }
          })

          setPersistedMessages(transformedMessages)

          // Set cached conversation data if available
          if (cachedConversation) {
            setConversation({
              ...cachedConversation,
              title: cachedConversation.title ?? undefined, // Convert null to undefined
              contextEntityType:
                cachedConversation.contextEntityType ?? undefined, // Convert null to undefined
              contextEntityId: cachedConversation.contextEntityId ?? undefined, // Convert null to undefined
              messages: [], // Initialize empty messages array to satisfy type requirement
              createdAt:
                typeof cachedConversation.createdAt === 'string'
                  ? cachedConversation.createdAt
                  : cachedConversation.createdAt instanceof Date
                    ? cachedConversation.createdAt.toISOString()
                    : new Date().toISOString(),
            })
          }

          setIsLoadingConversation(false)
          setConversationError(null)

          // Set basic pagination info (we'll update this if user requests more)
          setPaginationInfo({
            hasMore: cachedMessages.length >= limit,
            limit,
            total: cachedMessages.length,
          })

          // Check cache freshness - if cache is less than 2 minutes old, skip API call
          const cacheTimestamp = cachedConversation?.lastUpdated || 0
          const cacheAge = Date.now() - cacheTimestamp
          const isCacheFresh = cacheAge < 2 * 60 * 1000 // 2 minutes

          if (isCacheFresh) {
            const loadTime = performance.now() - startTime
            console.log(
              `⚡ [useAiConversation] Cache hit! Loaded ${cachedMessages.length} messages in ${loadTime.toFixed(2)}ms`
            )
            return
          }

          console.log(
            `🔄 [useAiConversation] Cache stale (${Math.round(cacheAge / 1000)}s old), refreshing...`
          )
        }
      }

      setIsLoadingConversation(!cursor) // Only show loading for initial load
      setIsLoadingMoreMessages(!!cursor) // Show "loading more" for pagination
      setConversationError(null)

      try {
        const apiStartTime = performance.now()
        console.log(
          `🔍 [useAiConversation] Loading conversation from API: ${convId}, limit: ${limit}, cursor: ${cursor}`
        )

        const url = new URL(
          `/api/aipane/conversations/${convId}`,
          window.location.origin
        )
        url.searchParams.set('limit', limit.toString())
        // Always exclude full execution steps for initial load — we fetch them lazily per-message in ReasoningTimeline,
        // so this greatly reduces the payload size and speeds up the initial conversation render.
        url.searchParams.set('includeSteps', 'false')
        if (cursor) {
          url.searchParams.set('cursor', cursor)
        }

        // Abort any existing request before starting a new one
        if (abortControllerRef.current) {
          abortControllerRef.current.abort()
        }

        // Create new AbortController for this request
        abortControllerRef.current = new AbortController()

        const response = await fetch(url.toString(), {
          signal: abortControllerRef.current.signal,
        })

        const apiTime = performance.now() - apiStartTime
        console.log(
          `⏱️ [useAiConversation] API call took ${apiTime.toFixed(2)}ms`
        )

        if (!response.ok) {
          throw new Error(`Failed to load conversation: ${response.status}`)
        }

        const data = await response.json()

        // Check if component is still mounted before updating state
        if (!isMountedRef.current) {
          console.log(
            '🚫 [useAiConversation] Component unmounted during fetch, skipping state update'
          )
          return
        }

        // Messages from API are ordered DESC (newest first). Reverse them to get
        // chronological order (oldest first) for correct display in the UI.
        const receivedMessages = (data.messages || [])
          .reverse()
          .map((m: any) => ({ ...m, role: (m.role || '').toLowerCase() }))

        if (!cursor) {
          // Initial load - set conversation and messages
          setConversation(data.conversation)
          setPersistedMessages(receivedMessages)
        } else {
          // Pagination load - prepend older messages
          setPersistedMessages(prev => [...receivedMessages, ...prev])
        }

        // Cache latest messages and conversation data
        const allMsgs = !cursor
          ? receivedMessages
          : [...receivedMessages, ...persistedMessages]
        if (convId) {
          setCachedMessages(
            convId,
            allMsgs as unknown as CachedMessage[],
            data.conversation as CachedConversation
          )
        }

        setPaginationInfo(
          data.pagination || {
            hasMore: false,
            limit: initialLimit,
            total: 0,
          }
        )

        const totalTime = performance.now() - startTime
        console.log(
          `✅ [useAiConversation] Loaded ${data.messages?.length || 0} messages in ${totalTime.toFixed(2)}ms (API: ${apiTime.toFixed(2)}ms)`
        )
      } catch (error) {
        // Don't treat AbortError as a real error - it's expected when requests are cancelled
        if (error instanceof Error && error.name === 'AbortError') {
          console.log(
            '🚫 [useAiConversation] Request was aborted (expected during cleanup)'
          )
          return // Exit early, don't set error state
        }

        console.error('Error loading conversation:', error)
        setConversationError(
          error instanceof Error ? error.message : 'Unknown error'
        )
      } finally {
        setIsLoadingConversation(false)
        setIsLoadingMoreMessages(false)
      }
    },
    [initialLimit]
  )

  // Load more messages (pagination)
  const loadMoreMessages = useCallback(async () => {
    if (!conversationId || !paginationInfo.hasMore || isLoadingMoreMessages)
      return

    await loadConversation(
      conversationId,
      paginationInfo.limit,
      paginationInfo.nextCursor
    )
  }, [
    conversationId,
    paginationInfo.hasMore,
    paginationInfo.limit,
    paginationInfo.nextCursor,
    isLoadingMoreMessages,
    loadConversation,
  ])

  // Refresh conversation
  const refreshConversation = useCallback(async () => {
    if (!conversationId) return
    await loadConversation(conversationId)
  }, [conversationId, loadConversation])

  // Initialize conversation loading
  useEffect(() => {
    // Only initialize if enabled and we have a valid conversation ID
    if (!enabled) {
      console.log(
        '🔍 [useAiConversation] Hook disabled, skipping initialization'
      )
      return
    }

    if (initialConversationId && initialConversationId.startsWith('thread_')) {
      console.log(
        '🔍 [useAiConversation] Initializing conversation:',
        initialConversationId
      )
      setConversationId(initialConversationId)

      // Add a small delay to ensure component is fully mounted and stable
      // This prevents race conditions during React's mounting cycle
      const timeoutId = setTimeout(() => {
        loadConversation(initialConversationId)
      }, 50) // Small delay to ensure stability

      // Cleanup timeout if component unmounts before it fires
      return () => clearTimeout(timeoutId)
    } else if (initialConversationId) {
      console.warn(
        '🔍 [useAiConversation] Invalid initial conversation ID:',
        initialConversationId
      )
    }
  }, [enabled, initialConversationId, loadConversation])

  // If the parent supplies a conversationId later (e.g. after a fetch),
  // update internal state and SWR key.
  useEffect(() => {
    if (
      initialConversationId &&
      initialConversationId !== conversationId &&
      initialConversationId.startsWith('thread_')
    ) {
      setConversationId(initialConversationId)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialConversationId])

  // Watch for new conversation ID from chat API responses
  useEffect(() => {
    // Extract conversation ID from chat response headers or data
    if (chat.messages.length > 0 && !conversationId) {
      // Try to extract conversation ID from the latest response
      // The chat API should include conversation ID in response
      const latestMessage = chat.messages[chat.messages.length - 1]
      if (latestMessage.role === 'assistant') {
        // Check if we can extract conversation ID from message metadata or response
        // For now, we'll rely on the parent component to set the conversation ID
        console.log(
          '🔍 [useAiConversation] New assistant message without conversation ID'
        )
      }
    }
  }, [chat.messages, conversationId])

  // Helper function to extract text content from UIMessage parts
  const extractTextContent = useCallback((message: Msg): string => {
    if (!message.parts) return ''

    return message.parts
      .filter(part => part.type === 'text')
      .map(part => (part as any).text)
      .join('')
  }, [])

  // Combine persisted messages (older) with streaming messages (new)
  const messages: ConversationMessage[] = useMemo(() => {
    // Merge persisted and chat.messages, avoiding duplicates by ID
    const persistedIds = new Set(persistedMessages.map(m => m.id))
    const newStreamingMessages: ConversationMessage[] = chat.messages
      .filter(m => !persistedIds.has(m.id))
      .map(m => ({
        id: m.id,
        role: (m.role || '').toLowerCase() as 'user' | 'assistant' | 'system',
        content: extractTextContent(m),
        createdAt: new Date().toISOString(),
      }))

    return [...persistedMessages, ...newStreamingMessages].filter(
      m => (m.role || '').toLowerCase() !== 'system'
    )
  }, [persistedMessages, chat.messages])

  // Side-effect: whenever merged messages change, update cache with AI SDK 5 optimizations
  useEffect(() => {
    if (conversationId && messages.length > 0) {
      // AI SDK 5 optimization: Debounce cache updates to reduce memory pressure
      const timeoutId = setTimeout(() => {
        setCachedMessages(conversationId, messages as unknown as CachedMessage[])
      }, 100) // Small delay to batch rapid updates
      
      return () => clearTimeout(timeoutId)
    }
  }, [conversationId, messages])

  // Update isStreaming flag for the last assistant message (optimized)
  const messagesWithStreamingFlag = useMemo(() => {
    if (messages.length === 0) return messages

    // Only update the last message to avoid re-rendering all messages
    const lastIndex = messages.length - 1
    const lastMessage = messages[lastIndex]
    const isLastAssistantMessage = lastMessage?.role === 'assistant'
    const shouldMarkAsStreaming =
      chat.status === 'streaming' && isLastAssistantMessage

    if (!shouldMarkAsStreaming) {
      // No streaming, return messages as-is if they don't have streaming flags
      const hasStreamingFlags = messages.some(m => 'isStreaming' in m)
      if (!hasStreamingFlags) return messages

      // Remove streaming flags from all messages
      return messages.map(message => {
        const { isStreaming: _isStreaming, ...messageWithoutStreaming } =
          message as any
        return messageWithoutStreaming
      })
    }

    // Only update the last message with streaming flag
    return messages.map((message, index) => {
      if (index === lastIndex) {
        return { ...message, isStreaming: true }
      }
      // Remove streaming flag from other messages if present
      const { isStreaming: _isStreaming, ...messageWithoutStreaming } =
        message as any
      return messageWithoutStreaming
    })
  }, [messages, chat.status])

  // AI SDK 5 compatibility layer - create missing properties for backward compatibility
  const [input, setInput] = useState('')

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setInput(e.target.value)
    },
    []
  )

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      if (input.trim() && chat.status !== 'streaming') {
        chat.sendMessage({ text: input.trim() })
        setInput('')
      }
    },
    [input, chat]
  )

  const append = useCallback(
    (message: { role: 'user' | 'assistant'; content: string }) => {
      if (message.role === 'user' && chat.status !== 'streaming') {
        chat.sendMessage({ text: message.content })
      }
    },
    [chat]
  )

  const isLoading = chat.status === 'streaming' || chat.status === 'submitted'

  // Scroll to message utility
  const scrollToMessage = useCallback((messageId: string) => {
    const element = document.getElementById(`message-${messageId}`)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'center' })
    }
  }, [])

  // Cleanup AbortController on unmount to prevent memory leaks
  useEffect(() => {
    // Set mounted flag to true on mount
    isMountedRef.current = true

    return () => {
      // Mark component as unmounted
      isMountedRef.current = false

      // Only abort if there's an active controller
      // Add a small delay to prevent race conditions during React's mounting/unmounting cycles
      const currentController = abortControllerRef.current
      if (currentController) {
        // Use setTimeout to defer the abort slightly, allowing any in-flight requests to complete
        // if the component is just being remounted (common in React StrictMode or tab switching)
        setTimeout(() => {
          // Double-check the controller is still the same one we intended to abort
          if (abortControllerRef.current === currentController) {
            currentController.abort()
            abortControllerRef.current = null
          }
        }, 10) // Small delay to prevent race conditions
      }
    }
  }, [])

  return {
    conversation,
    isLoadingConversation,
    conversationError,
    messages: messagesWithStreamingFlag,
    hasMoreMessages: paginationInfo.hasMore,
    isLoadingMoreMessages,
    loadMoreMessages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    append,
    refreshConversation,
    scrollToMessage,
    // Real-time step streaming
    liveSteps,
    isStreamingSteps,
  }
}
